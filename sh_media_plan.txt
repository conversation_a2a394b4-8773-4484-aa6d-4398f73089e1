ПЛАН РАЗРАБОТКИ Telegram‑бота «sh: Media»
=============================================

Цель
----
Создать минималистичного Telegram‑бота «sh: Media» на **aiogram** c компактным, модульным (≤ 5 файлов) кодом.
Бот:
* `/img <prompt>` — генерирует изображение через `images.generate`.
* Ответ на изображение или сообщение с прикреплённым изображением + `/img <prompt>` — редактирует существующее(‑ие) изображение(я) через `images.edit`.
* При нескольких фото в сообщении — все передаются одним запросом, prompt берётся из текста после команды у **первого** фото.
* Использует API по примеру, модель `gpt-image-1`, API‑ключ жёстко задан в `media_config.py`.

Структура проекта (предварительно)
----------------------------------
```
media_config.py      # токены и константы
media_bot.py         # точка входа, Router, запуск
media_handlers.py    # обработчики команд и сообщений
media_openai.py      # обёртки над images.generate / images.edit
media_utils.py       # вспом. функции (валидация, логика выбора режима)
```

Этап 1 — Инициализация проекта
------------------------------
**Задачи**
2. Создать `media_config.py` и вписать API‑ключ `cody-V_AtIw73yP1d_rK9DmlLlZgOO2PzWKONwwFZ6sr40gWRZatPF_QzoxK_HAR-6PQIEPcVkL9kSrwfJwGdoafcdA`.
3. Реализовать `media_bot.py` с базовым `Bot`, `Dispatcher`, обработчиком `/start`, логирование.
4. Настроить `.gitignore`, `README.md` с инструкцией запуска.

**Тесты**
- Запуск `python media_bot.py` → бот отвечает на `/start`.
- Проверка импорта всех модулей без ошибок (`python -m pytest -q` заглушка).

**🔧 Изменения, внесённые на этапе 1**  
(заполнит ИИ по завершении).

---

Этап 2 — Генерация изображений (`/img` без вложений)
---------------------------------------------------
**Задачи**
1. В `media_openai.py` написать функцию `generate_image(prompt:str)->bytes`.
2. В `media_handlers.py` реализовать хэндлер `/img <prompt>` (без вложений/реплая):  
   * парсинг prompt;  
   * вызов `generate_image`;  
   * отправка результата как фото.
3. Добавить обработку ошибок API и таймаутов.

**Тесты**
- Отправить `/img cat astronaut` → бот присылает 1 фото.
- Инъекция пустого prompt (`/img`) → корректное сообщение об ошибке.
- Задержка API (симулировать) → отправляется сообщение «Попробуйте позже».

**🔧 Изменения, внесённые на этапе 2**  
(заполнит ИИ).

---

Этап 3 — Редактирование изображений
-----------------------------------
**Задачи**
1. В `media_openai.py` реализовать `edit_images(prompt:str, images:list[bytes])->bytes`.
2. В `media_utils.py` добавить функцию извлечения всех фото из сообщения или реплая (в порядке Telegram).
3. В `media_handlers.py` реализовать логику:  
   * если сообщение **реплай** на фото **или** содержит фото и команду `/img <prompt>` — собрать все фото, передать одной пачкой в `edit_images`.
   * При нескольких фото prompt берётся из текста после команды у первого фото.
4. Валидировать размер изображений (< 20 MB каждый), временно сохранять их для передачи в OpenAI.

**Тесты**
- Реплай на одно фото + `/img add sunglasses` → возвращается отредактированное фото.
- Сообщение с 3 фото + `/img invert colors` → приходит ответ с 3 изменёнными фото.
- Неверный формат вложений (гиф, документ) → бот сообщает об ошибке.

**🔧 Изменения, внесённые на этапе 3**  
(заполнит ИИ).

---

Этап 4 — Завершение и деплой
---------------------------
**Задачи**
1. Добавить логирование запросов/ответов OpenAI (без base64‑данных).
2. Обновить `README.md`: инструкции, лимиты, возможные ошибки.
3. Подготовить `requirements.txt`, опционально `Dockerfile`.

**Тесты**
- Прогнать полный регресс: сценарии из Этапов 2‑3.
- Линтеры (`flake8`), `mypy` — чистый проход.
- Бот развёрнут на VPS/Render, успешно отвечает.

**🔧 Изменения, внесённые на этапе 4**  
(заполнит ИИ).

---

Конец плана.
